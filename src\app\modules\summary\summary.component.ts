import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SummaryData } from './summary.model';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';
import { SummaryEndpointService } from './summary-endpoint.service';
import { HeaderInfoService, StoreService } from '@core/services';
import { Subject, takeUntil } from 'rxjs';
import { SeModalService, SeProgressModal } from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import {
  SelfAssessmentState,
  SelfAssessmentStatus,
} from '@core/models/self-assessment-status.model';

@Component({
  selector: 'app-summary',
  templateUrl: './summary.component.html',
  styleUrls: [],
})
export class SummaryComponent implements OnInit, OnD<PERSON>roy {
  protected summaryDataList: SummaryData[] = [];
  // OBSERVABLES CONTROL DESTROY
  private destroyed$: Subject<void> = new Subject();
  // ALERT
  protected descriptionError: string | undefined;
  protected readonly alertMessage = this.translateService.instant(
    'SE_DECINF_MF.MODULE_PRESENTATION_RESULT.ALERT.ERROR_DESCRIPTION',
    { link: this.getFormLInk() },
  );
  constructor(
    private router: Router,
    private summaryEndpointService: SummaryEndpointService,
    private storeService: StoreService,
    private translateService: TranslateService,
    private modalService: SeModalService,
    private header: HeaderInfoService,
  ) {
    // Initialization if needed
  }

  ngOnInit(): void {
    const idTramit = this.storeService.idTramit;
    this.summaryEndpointService
      .getSummaryData(idTramit!)
      .subscribe((response) => {
        if (response.content) {
          const data = response.content;
          const LABEL_PREFIX = 'SE_DECINF_MF.MODULE_SUMMARY.';
          this.summaryDataList = [
            { label: LABEL_PREFIX + 'PRESENTER', value: data.presentador },
            { label: LABEL_PREFIX + 'DECLARANT', value: data.declarant },
            {
              label: LABEL_PREFIX + 'MODEL',
              value: this.storeService.modelDescription || data.model,
            },
            { label: LABEL_PREFIX + 'TAX_YEAR', value: data.exercici },
            { label: LABEL_PREFIX + 'FILE_NAME', value: data.nomArxiu },
            {
              label: LABEL_PREFIX + 'CODI_ASSSEGURADORA',
              value: data.codiAssseguradora,
            },
            {
              label: LABEL_PREFIX + 'TOTAL_REGISTRES',
              value: data.totalRegistres,
            },
          ];
          // parte variable por modelo
          this.summaryDataList.push(
            ...Object.entries(data.informacioResumModel)
              .filter(([, value]) => value)
              .map(([key, value]) => ({
                label: `${LABEL_PREFIX}${this.camelToSnakeUpperCase(key)}`,
                value,
              })),
          );
        }
      });
  }

  private camelToSnakeUpperCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1_$2').toUpperCase();
  }

  private getFormLInk(): string {
    return this.translateService.currentLang === 'ca'
      ? 'https://seu.atc.gencat.cat/ca/Utilitats/Pagines/FormulariIncidenciaJira.aspx'
      : 'https://seu.atc.gencat.cat/es/Utilitats/Paginas/FormulariIncidenciaJira.aspx';
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.YEAR_DECLARATION]);
  }

  protected onSubmit(): void {
    this.summaryEndpointService
      .postSelfAssesment(this.storeService.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result) => {
        if (result.content) {
          const idSelfassesment = result.content;
          const progressModal: SeProgressModal = {
            interval: 10,
            message: this.translateService.instant(
              'SE_DECINF_MF.MODULE_PRESENTATION_RESULT.PRESENTATION',
            ),
          };
          const modalRef = this.modalService.openProgressModal(
            progressModal.interval!,
            progressModal.message!,
          );

          this.checkStatusAutoliquidacio(modalRef, idSelfassesment);
        }
      });
  }

  private checkStatusAutoliquidacio = (
    modalRef: NgbModalRef,
    idAutoliquidacio: string,
  ): void => {
    modalRef.componentInstance.intervalOutput
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.summaryEndpointService
          .getStatusAutoliquidacio(idAutoliquidacio)
          .pipe(takeUntil(this.destroyed$))
          .subscribe({
            next: (response) => {
              if (response.content) {
                const status = response.content.estat;
                this.header.status = status as SelfAssessmentStatus;
                this.header.presentationDate =
                  response?.content.dataPresentacio;
                if (this.isPresentat(status)) {
                  this.storeService.numJustificant =
                    response.content.numJustificant;
                  modalRef.close();
                  this.router.navigate([AppRoutes.PRESENTATION_RESULT]);
                } else if (this.hasError(status)) {
                  this.descriptionError = response?.content?.errors
                    ? response.content.errors[0].description
                    : '';
                  modalRef.close();
                }
              }
            },
          });
      });
  };

  private isPresentat = (estat: SelfAssessmentState | undefined): boolean =>
    estat === SelfAssessmentState.PRESENTAT;
  private hasError = (estat: SelfAssessmentState | undefined): boolean =>
    estat === SelfAssessmentState.PRESENTACIO_ERROR ||
    estat === SelfAssessmentState.ERROR;

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
