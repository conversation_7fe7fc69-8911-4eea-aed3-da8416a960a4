import { Component, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { TaxYearDeclarationEndpointService } from '../services/tax-declaration-endpoint.service';
import { PutTaxDeclarationData } from '../tax-year-declaration.model';
import { StoreService } from '@core/services';

@Component({
  selector: 'app-insurance-panel',
  templateUrl: './insurance-panel.component.html',
  styleUrls: [],
})
export class InsurancePanelComponent implements OnInit, OnDestroy {
  @Input() componentForm: FormGroup | undefined;
  @Output() dadaAdicionalSaved: Subject<boolean> = new Subject<boolean>();
  private destroyed$: Subject<void> = new Subject();

  constructor(
    private taxDeclarationEndpointService: TaxYearDeclarationEndpointService,
    private store: StoreService,
  ) {
    // Empty constructor
  }

  ngOnInit(): void {
    this.componentForm
      ?.get('dadaAdicional')
      ?.valueChanges.pipe(
        takeUntil(this.destroyed$),
        debounceTime(300),
        distinctUntilChanged(),
      )
      .subscribe((value: string) => {
        if (this.componentForm?.get('dadaAdicional')?.valid) {
          const body: PutTaxDeclarationData = {
            dadaAdicional: value,
            model: this.componentForm?.get('model')?.value,
            exercici: this.componentForm?.get('exercici')?.value,
            codiImpost: this.componentForm?.get('codiImpost')?.value,
            substitutiva: this.componentForm?.get('substitutiva')?.value,
            fitxer: this.componentForm?.get('fitxer')?.value,
          };
          this.taxDeclarationEndpointService
            .putTaxDeclarationData(body, this.store.idTramit!)
            .subscribe(() => {
              this.dadaAdicionalSaved.next(true);
            });
        }
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
