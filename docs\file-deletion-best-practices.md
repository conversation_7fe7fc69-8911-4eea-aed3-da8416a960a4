# Mejores Prácticas para Eliminación de Archivos

## Problema Identificado

Al ejecutar `this.deleteFileByDocId$.next(fileId)`, las peticiones HTTP DELETE al microservicio se cancelan ocasionalmente debido a problemas de red, navegación rápida, o timeouts.

## Soluciones Implementadas

### 1. Retry Logic con Backoff Exponencial

```typescript
private deleteFileWithRetry(fileId: string): void {
  timer(100)
    .pipe(
      takeUntil(this.destroyed$),
      retry({
        count: 3, // Reintentar hasta 3 veces
        delay: (error, retryCount) => {
          console.warn(`Intento ${retryCount} falló, reintentando en ${retryCount * 1000}ms...`, error);
          return timer(retryCount * 1000); // Backoff exponencial: 1s, 2s, 3s
        }
      }),
      catchError((error) => {
        console.error('Eliminación falló después de todos los reintentos:', error);
        return of(null);
      })
    )
    .subscribe({
      next: () => this.deleteFileByDocId$.next(fileId),
      error: (error) => console.error('Error inesperado:', error)
    });
}
```

### 2. Método de Eliminación Directa como Fallback

```typescript
private deleteFileDirectly(fileId: string): Observable<any> {
  return this.taxDeclarationEndpointService.deleteFileByDocId(fileId)
    .pipe(
      takeUntil(this.destroyed$),
      retry({
        count: 3,
        delay: (error, retryCount) => timer(retryCount * 1000)
      }),
      catchError((error) => {
        console.error('Eliminación directa falló:', error);
        return of(null);
      })
    );
}
```

### 3. Configuración de Timeout Extendido

```typescript
deleteFileByDocId(fileId: string): Observable<SeHttpResponse> {
  return this.httpService.delete({
    method: 'delete',
    baseUrl: environment.baseUrlDocuments,
    url: `/document/${fileId}`,
    clearExceptions: true,
    spinner: false, // Evitar bloqueo de UI
    timeout: 30000, // 30 segundos de timeout
  });
}
```

### 4. Manejo Adecuado de Observables

```typescript
ngOnDestroy(): void {
  // Completar todos los observables para prevenir memory leaks y cancelar requests pendientes
  this.destroyed$.next();
  this.destroyed$.complete();
  this.deleteFileByDocId$.complete();
}
```

## Mejores Prácticas Generales

### 1. Siempre usar `takeUntil(this.destroyed$)`
- Previene memory leaks
- Cancela automáticamente requests cuando el componente se destruye

### 2. Implementar Retry Logic para operaciones críticas
- Usar `retry()` con configuración de delay
- Implementar backoff exponencial para evitar spam de requests

### 3. Manejar errores apropiadamente
- Usar `catchError()` para manejar fallos
- Proporcionar fallbacks cuando sea posible
- Loggear errores para debugging

### 4. Configurar timeouts apropiados
- Usar timeouts más largos para operaciones de red
- Considerar la latencia de red en entornos de producción

### 5. Proporcionar feedback al usuario
- Mostrar spinners o indicadores de progreso
- Notificar al usuario sobre errores de red
- Permitir reintentos manuales cuando sea apropiado

## Debugging

### Logs útiles para identificar problemas:
```typescript
console.log('Attempting to delete file with ID:', fileId);
console.warn(`File deletion attempt ${retryCount} failed, retrying...`, error);
console.error('File deletion failed after all retry attempts:', error);
```

### Verificar en Network Tab del navegador:
- Status de las peticiones HTTP
- Tiempo de respuesta
- Errores de cancelación (status cancelled)

## Configuración de Entorno

Asegurar que las URLs base estén correctamente configuradas:

```typescript
// environment.ts
export const environment = {
  baseUrlDocuments: '/api/documents',
  // ... otras configuraciones
};
```

## Testing

### Casos de prueba recomendados:
1. Eliminación exitosa en primera tentativa
2. Eliminación exitosa después de reintentos
3. Fallo completo después de todos los reintentos
4. Cancelación por navegación del usuario
5. Timeout de red

### Simular condiciones de red:
- Usar Chrome DevTools para simular conexiones lentas
- Probar con latencia alta
- Simular desconexiones temporales
