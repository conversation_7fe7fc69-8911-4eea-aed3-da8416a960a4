{"/mf/pt-commons-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-commons-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-commons-mf": ""}}, "/mf/se-documents-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-documents-mf": ""}}, "/mf/se-tributs-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-tributs-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-tributs-mf": ""}}, "/mf/se-gestions-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-gestions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-gestions-mf": ""}}, "/mf/se-seguretat-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-seguretat-mf": ""}}, "/mf/se-presentacions-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-presentacions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-presentacions-mf": ""}}, "/api/tributs/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/tributs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/tributs": ""}}, "/api/declaracions-informatives/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/declaracions-informatives", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/declaracions-informatives": ""}}, "/api/documents/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/documents", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/documents": ""}}, "/api/seguretat-admin/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/seguretat-admin", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat-admin": ""}}, "/api/seguretat/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/seguretat", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat": ""}}, "/api/presentacions/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/presentacions", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/presentacions": ""}}, "/api/contribuent/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/contribuent", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/contribuent": ""}}}