import { SelfAssessmentState } from '@core/models/self-assessment-status.model';

export interface SummaryData {
  label: string;
  value: string | number | boolean;
}

interface InformacioResumModel {
  [key: string]: string | number;
}

export interface SummaryResponse {
  presentador: string;
  declarant: string;
  model: string;
  exercici: string;
  dadaAdicional: null;
  codiAssseguradora: string;
  nomArxiu: string;
  totalRegistres: string;
  informacioResumModel: InformacioResumModel;
}

export interface Autoliquidacio {
  idAutoliquidacio: string;
  idDocuments: string[];
  numJustificant: string;
  quotaLiquida: number;
  subjectePassiu: SubjectePassiu;
  tipus: null;
  estat: SelfAssessmentState;
  dataPresentacio: null;
  dataIncompliment: null;
  dataFinTermini: null;
  dataTerminiPresentacio: null;
  errors?: AutoliquidacioError[];
}

export interface AutoliquidacioError {
  code: string;
  date: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

interface SubjectePassiu {
  nif: string;
  nom: string;
}
