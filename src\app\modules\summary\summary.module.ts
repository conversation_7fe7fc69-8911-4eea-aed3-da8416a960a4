import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NumberFormatPipe } from './number-format.pipe';

import {
  SeAlertModule,
  SeButtonModule,
  SePanelModule,
} from 'se-ui-components-mf-lib';
import { SummaryComponent } from './summary.component';

const routes: Routes = [
  {
    path: '',
    component: SummaryComponent,
    data: {
      title: 'SE_DECINF_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP3',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [SummaryComponent, NumberFormatPipe],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    SeButtonModule,
    SeAlertModule,
    SePanelModule,
  ],
})
export class SummaryModule {}
