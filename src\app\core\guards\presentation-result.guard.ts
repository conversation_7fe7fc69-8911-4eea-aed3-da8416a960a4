import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

@Injectable({
  providedIn: 'root',
})
export class PresentationResultGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private router: Router,
  ) {}

  canActivate(): boolean {
    const numJustificant = this.storeService.numJustificant;
    const modelDescription = this.storeService.modelDescription;
    const idTramit = this.storeService.idTramit;

    if (numJustificant) {
      return true;
    }

    if (modelDescription) {
      this.router.navigate([AppRoutes.SUMMARY]);
      return false;
    }

    if (idTramit) {
      this.router.navigate([AppRoutes.YEAR_DECLARATION]);
      return false;
    }

    this.router.navigate([AppRoutes.PARTICIPANTS]);
    return false;
  }
}
