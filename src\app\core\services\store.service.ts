import { Injectable } from '@angular/core';
import { SelfAssessmentStatus } from '@core/models/self-assessment-status.model';
import {
  Nullable,
  SeDataStorageService,
  SeHeaderInfoItem,
} from 'se-ui-components-mf-lib';
import { TaxPayer } from 'src/app/modules/participants/models';

/* Storage keys */
const ID_TRAMIT = 'DECINF_ID_TRAMIT';
const SELECTED_TAXPAYER = 'DECINF_SELECTED_TAXPAYER';
const HEADER = 'DECINF_HEADER';
const STATUS = 'DECINF_STATUS';
const RESPONSIBLE_DECLARATION_CHECKED =
  'DECINF_RESPONSIBLE_DECLARATION_CHECKED';
const PRESENTATION_DATE = 'DECINF_PRESENTATION_DATE';
const TAX_YEAR = 'DECINF_TAX_YEAR';
const CURRENT_LANG = 'DECINF_CURRENT_LANG';
const MODEL_DESCRIPTION = 'DECINF_MODEL_DESCRIPTION';
const NUM_JUSTIFICANT = 'DECINF_NUM_JUSTIFICANT';

@Injectable({
  providedIn: 'root',
})
export class StoreService {
  constructor(private storage: SeDataStorageService) {
    // Intencionadamente vacío
  }

  get idTramit(): Nullable<string> {
    return this.storage.getItem(ID_TRAMIT);
  }
  set idTramit(idTramit: Nullable<string>) {
    this.storage.setItem(ID_TRAMIT, idTramit);
  }

  get selectedTaxpayer(): Nullable<TaxPayer> {
    return this.storage.getItem(SELECTED_TAXPAYER);
  }
  set selectedTaxpayer(taxpayer: Nullable<TaxPayer>) {
    this.storage.setItem(SELECTED_TAXPAYER, taxpayer);
  }

  get header(): Nullable<SeHeaderInfoItem>[] {
    return this.storage.getItem(HEADER);
  }
  set header(header: Nullable<SeHeaderInfoItem>[]) {
    this.storage.setItem(HEADER, header);
  }
  get status(): SelfAssessmentStatus {
    return this.storage.getItem(STATUS);
  }
  set status(header: SelfAssessmentStatus) {
    this.storage.setItem(STATUS, header);
  }
  get responsibleDeclaration(): Nullable<boolean> {
    return this.storage.getItem(RESPONSIBLE_DECLARATION_CHECKED);
  }
  set responsibleDeclaration(check: Nullable<boolean>) {
    this.storage.setItem(RESPONSIBLE_DECLARATION_CHECKED, check);
  }
  get presentationDate(): Nullable<string> {
    return this.storage.getItem(PRESENTATION_DATE);
  }
  set presentationDate(presentationDate: Nullable<string>) {
    this.storage.setItem(PRESENTATION_DATE, presentationDate);
  }
  get taxYear(): Nullable<number> {
    return this.storage.getItem(TAX_YEAR);
  }
  set taxYear(taxYear: Nullable<number>) {
    this.storage.setItem(TAX_YEAR, taxYear);
  }
  get currentLang(): Nullable<string> {
    return this.storage.getItem(CURRENT_LANG);
  }
  set currentLang(lang: Nullable<string>) {
    this.storage.setItem(CURRENT_LANG, lang);
  }
  get modelDescription(): Nullable<string> {
    return this.storage.getItem(MODEL_DESCRIPTION);
  }
  set modelDescription(modelDescription: Nullable<string>) {
    this.storage.setItem(MODEL_DESCRIPTION, modelDescription);
  }
  get numJustificant(): Nullable<string> {
    return this.storage.getItem(NUM_JUSTIFICANT);
  }
  set numJustificant(numJustificant: Nullable<string>) {
    this.storage.setItem(NUM_JUSTIFICANT, numJustificant);
  }

  clearStore(): void {
    this.storage.deleteItem(ID_TRAMIT);
    this.storage.deleteItem(SELECTED_TAXPAYER);
    this.storage.deleteItem(HEADER);
    this.storage.deleteItem(STATUS);
    this.storage.deleteItem(RESPONSIBLE_DECLARATION_CHECKED);
    this.storage.deleteItem(PRESENTATION_DATE);
    this.storage.deleteItem(TAX_YEAR);
    this.storage.deleteItem(CURRENT_LANG);
    this.storage.deleteItem(MODEL_DESCRIPTION);
  }
}
