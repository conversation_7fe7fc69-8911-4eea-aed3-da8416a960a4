export interface ContestableActDocument {
  type: string;
  subtype?: string;
  name?: string;
  description?: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}

export interface DeclarationTypes {
  model: string;
  descripcio: string;
  codiImpost: string;
}

export interface Substitutiva {
  numJustificant: string;
  declarant: string;
  dataPresentacio: Date;
  estat: string;
  samePresenter?: boolean;
}

export interface ShowSubstitutivePanelInfo {
  samePresenter: boolean;
  substitutive: Substitutiva | null;
  showPanel: boolean;
  hasDeclaracioPrevia: boolean;
}

export interface SubstitutivaData {
  hasDeclaracioPrevia: boolean;
  declaracio: Substitutiva;
}

export interface File {
  idPadoct: string;
  idDocument: string;
  nom: string;
  pes: number;
  descripcio: string;
  tipusDocument: string;
}

export interface PutTaxDeclarationData {
  model: string;
  exercici: string;
  substitutiva: Substitutiva;
  codiImpost: string;
  dadaAdicional?: string;
  fitxer: File;
}

export interface GetTaxDeclarationData {
  model: string;
  exercici: string;
  substitutiva: Substitutiva;
  codiImpost: string;
  dadaAdicional?: string;
}

export const EstatFitxer = {
  NO_VALIDAT: 'NO_VALIDAT',
  VALIDANT: 'VALIDANT',
  VALIDAT: 'VALIDAT',
  VALIDACIO_ERROR: 'VALIDACIO_ERROR',
  CANCELLAT: 'CANCELLAT',
} as const;

// Opcional: también puedes exportar el type
export type EstatFitxerType = keyof typeof EstatFitxer;

export interface ValidationProgress {
  estat: EstatFitxerType;
  percentatgeProgres: number;
}
