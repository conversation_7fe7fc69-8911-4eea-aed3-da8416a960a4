<div class="d-flex flex-column gap-4">
  <se-alert
    *ngIf="descriptionError"
    [type]="'error'"
    [closeButton]="true"
    [title]="
      'SE_DECINF_MF.MODULE_PRESENTATION_RESULT.ALERT.ERROR_TITLE' | translate
    "
  >
    <p>{{ descriptionError }}</p>
    <p [innerHTML]="alertMessage"></p>
  </se-alert>
  <se-panel
    [id]="'summary_panel_id'"
    [title]="'SE_DECINF_MF.MODULE_SUMMARY.TITLE' | translate"
    [colapsible]="false"
    [collapsed]="false"
    [panelTheme]="'default'"
  >
    <ng-container *ngFor="let data of summaryDataList">
      <div class="row" *ngIf="data.value">
        <p class="col-12 col-md-4 fw-bold">{{ data.label | translate }}</p>
        <p class="col-12 col-md-8">{{ data.value | numberFormat }}</p>
      </div>
    </ng-container>
  </se-panel>

  <!--  BUTTONS -->
  <section class="d-flex justify-content-between flex-row">
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
      {{ 'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate }}
    </se-button>
    <se-button (onClick)="onSubmit()">
      {{ 'SE_DECINF_MF.MODULE_SUMMARY.SUBMIT' | translate }}
    </se-button>
  </section>
</div>
