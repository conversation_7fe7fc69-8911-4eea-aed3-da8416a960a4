import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Constant, Models } from '@core/models/constants.enum';
import {
  Column,
  SeDocumentsService,
  SeDropdownOption,
  SeHttpResponse,
  SeModal,
  SeModalOutputEvents,
  SeModalService,
  SeProgressModal,
  iDocumentPadoct,
} from 'se-ui-components-mf-lib';
import {
  ContestableActDocument,
  DeclarationTypes,
  EstatFitxer,
  File,
  GetTaxDeclarationData,
  PutTaxDeclarationData,
  ShowSubstitutivePanelInfo,
  Substitutiva,
  ValidationProgress,
} from './tax-year-declaration.model';
import { TranslateService } from '@ngx-translate/core';
import { TaxYearDeclarationService } from './services/tax-declaration.service';
import { TaxYearDeclarationEndpointService } from './services/tax-declaration-endpoint.service';
import {
  BehaviorSubject,
  Observable,
  Subject,
  Subscription,
  takeUntil,
  timer,
  retry,
  catchError,
  of,
  delay,
} from 'rxjs';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';
import { HeaderInfoService, StoreService } from '@core/services';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { SelfAssessmentState } from '@core/models/self-assessment-status.model';

@Component({
  selector: 'app-tax-year-declaration',
  templateUrl: './tax-year-declaration.component.html',
  styleUrls: ['./tax-year-declaration.component.scss'],
})
export class TaxYearDeclarationComponent implements OnInit, OnDestroy {
  // FORM
  protected componentForm: FormGroup;
  // OBSERVABLES CONTROL DESTROY
  private destroyed$: Subject<void> = new Subject();
  // FORM CONTROL VALUES
  private currentModelCode: string = '';
  private currentYear: number | undefined = undefined;
  protected optionsModelList: SeDropdownOption[] = [];
  protected optionsTaxYearList: SeDropdownOption[] = [];
  // FILE UPLOAD
  protected showPanelFileUpload: boolean = false;
  protected fileUploadPanelTitle: string = '';
  protected downloadFileTemplateUrl: string | null = null;
  protected readonly functionalModule: string = Constant.NAME;
  protected readonly acceptedFiles = ['csv'];
  protected readonly contestableActDocument: ContestableActDocument[] = [
    {
      type: Constant.TAX_DOCUMENT_TYPE,
      name: Constant.TAX_DOCUMENT_TYPE,
      description: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.NOM_DOCUMENT',
      ),
      allowedFiles: this.acceptedFiles,
    },
  ];
  protected showValidateFileErrorsId: string | null = null;
  protected get idTramit(): string | null | undefined {
    return this.storeService.idTramit;
  }
  // TABLE COLUMNS
  protected tableColumns: Column[] =
    this.taxYearDeclarationService.getTableColumns();
  protected modalTableColumns: Column[] =
    this.taxYearDeclarationService.getModalTableColumns();
  // DELETE FILE
  protected deleteFileByDocId$: Subject<string> = new Subject();
  // Substitutive
  protected substitutiveData: ShowSubstitutivePanelInfo;
  // Code Insurance
  protected showPanelCodeInsurance: boolean = false;

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private taxYearDeclarationService: TaxYearDeclarationService,
    private taxDeclarationEndpointService: TaxYearDeclarationEndpointService,
    private storeService: StoreService,
    private router: Router,
    private modalService: SeModalService,
    private documentService: SeDocumentsService,
    private headerService: HeaderInfoService,
    private cdr: ChangeDetectorRef,
  ) {
    this.headerService.resetYearDeclaration();
    this.substitutiveData = {
      showPanel: false,
      substitutive: null,
      samePresenter: false,
      hasDeclaracioPrevia: false,
    };
    this.componentForm = this.fb.group({
      model: ['', Validators.required],
      codiImpost: ['', Validators.required],
      modelCode: ['', Validators.required],
      exercici: ['', Validators.required],
      dadaAdicional: ['', Validators.pattern(/^[A-Za-z]\d{4}$/)],
      substitutiva: this.fb.group({
        numJustificant: [],
        declarant: [],
        dataPresentacio: [],
        estat: [],
      }),
      anex: this.fb.group(
        {
          idPadoct: ['', Validators.required],
          idDocument: ['', Validators.required],
          nom: [''],
          pes: [''],
          descripcio: [''],
          tipusDocument: [''],
          extension: [''],
        },
        Validators.required,
      ),
    });
  }

  ngOnInit(): void {
    this.getDeclarationTypes();
    this.getTaxYearDeclarationData();
  }

  ngOnDestroy(): void {
    // Complete all observables to prevent memory leaks and cancel pending requests
    this.destroyed$.next();
    this.destroyed$.complete();
    this.deleteFileByDocId$.complete();

    console.log('TaxYearDeclarationComponent destroyed - all pending requests cancelled');
  }

  private getDeclarationTypes(): void {
    this.taxDeclarationEndpointService
      .getDeclarationTypes()
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            response.content.forEach((model: DeclarationTypes) => {
              this.optionsModelList.push({
                id: `${model.model}_${model.codiImpost}`,
                label: model.descripcio,
              });
            });
          }
        },
        error: (error) => {
          console.error('Error fetching declaration types:', error);
        },
      });
  }

  protected downloadFileErrors(): void {
    this.taxDeclarationEndpointService
      .getDownloadErrorTemplate(this.showValidateFileErrorsId!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            this.documentService.openFile(
              response.content,
              'text/csv',
              `errors_${this.componentForm.get('model')?.value}_${this.componentForm.get('exercici')?.value}.csv`,
            );
          }
        },
        error: (error) => {
          console.error('Error downloading error template:', error);
        },
      });
  }

  private getModelDescription(modelCode: string): string {
    const model = this.optionsModelList.find(
      (option) => option.id === modelCode,
    );
    return model ? model.label : '';
  }

  protected onExerciciChange(event: number): void {
    if (
      this.currentYear &&
      (this.componentForm.get('anex')?.get('idDocument')?.value ||
        this.componentForm.get('substitutiva')?.get('numJustificant')?.value)
    ) {
      this.showDeleteDataWarningModal(true);
    } else {
      this.currentYear = event;
      this.saveData().subscribe({
        next: () => {
          this.verifyCodeInsurance(this.componentForm.get('model')?.value);
        },
      });
    }
    this.showPanelFileUpload = true;
  }

  protected onModelChange(event: string): void {
    if (this.componentForm.get('model')?.value) {
      this.showDeleteDataWarningModal();
    } else {
      this.setModelCode(event);
    }
  }

  private setModelCode(modelCode: string): void {
    this.currentModelCode = modelCode;
    const [model, codiImpost] = modelCode.split('_');
    this.componentForm.get('model')?.setValue(model);
    this.componentForm.get('codiImpost')?.setValue(codiImpost);
    this.getTaxYearsByImpost(model);
    this.fileUploadPanelTitle = this.getModelDescription(modelCode);
    this.downloadFileTemplateUrl = this.translateService.instant(
      'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_2',
      {
        url: this.taxYearDeclarationService.getModelUrl(
          model,
          this.translateService.currentLang as 'ca' | 'es',
        ),
      },
    );
  }

  private verifyCodeInsurance(model: string): void {
    if (model === Models.MODEL_673) {
      this.componentForm
        .get('dadaAdicional')
        ?.setValidators([
          Validators.required,
          Validators.pattern(/^[A-Za-z]\d{4}$/),
        ]);
      this.componentForm.get('dadaAdicional')?.updateValueAndValidity();
      this.showPanelCodeInsurance = true;
    } else {
      this.componentForm.get('dadaAdicional')?.clearValidators();
      this.componentForm.get('dadaAdicional')?.setValue('');
      this.componentForm.get('dadaAdicional')?.updateValueAndValidity();
      this.showPanelCodeInsurance = false;
      if (this.componentForm.get('exercici')?.value) {
        this.searchSubstituteiveDeclaration();
      }
    }
  }

  protected onSubstitutiveChange(event: Substitutiva | null): void {
    if (!event) {
      this.componentForm.get('substitutiva')?.reset();
      return;
    }
    this.componentForm.get('substitutiva')?.patchValue(event);
    this.cdr.detectChanges();
  }

  private getTaxYearDeclarationData(): void {
    this.taxDeclarationEndpointService
      .getDeclarationData(this.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            const data: GetTaxDeclarationData = response.content;
            this.componentForm.get('model')?.setValue(data.model);
            this.componentForm.get('codiImpost')?.setValue(data.codiImpost);
            this.componentForm
              .get('modelCode')
              ?.setValue(`${data.model}_${data.codiImpost}`);
            this.currentModelCode = `${data.model}_${data.codiImpost}`;
            this.getTaxYearsByImpost(data.model);
            if (data.exercici) {
              this.componentForm.get('exercici')?.setValue(data.exercici);
              this.showPanelFileUpload = true;
              this.currentYear = Number(data.exercici);
            }
            if (data.dadaAdicional) {
              this.componentForm
                .get('dadaAdicional')
                ?.setValue(data.dadaAdicional);
            }
            if (data.substitutiva?.numJustificant) {
              this.substitutiveData = {
                substitutive: data.substitutiva,
                samePresenter: true,
                showPanel: true,
                hasDeclaracioPrevia: true,
              };
              this.componentForm
                .get('substitutiva')
                ?.get('numJustificant')
                ?.addValidators(Validators.required);
              this.componentForm
                .get('substitutiva')
                ?.get('numJustificant')
                ?.updateValueAndValidity();
            }
            if (data.model) {
              this.fileUploadPanelTitle = this.getModelDescription(
                `${data.model}_${data.codiImpost}`,
              );
              this.verifyCodeInsurance(data.model);
              this.downloadFileTemplateUrl = this.translateService.instant(
                'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_2',
                {
                  url: this.taxYearDeclarationService.getModelUrl(
                    data.model,
                    this.translateService.currentLang as 'ca' | 'es',
                  ),
                },
              );
            }
            this.componentForm.updateValueAndValidity();
          }
        },
        error: (error) => {
          console.error('Error fetching tax declaration data:', error);
        },
      });
  }

  private showDeleteDataWarningModal(maintainYear: boolean = false): void {
    const modal: SeModal = this.taxYearDeclarationService.getWarningModalData();

    const modalRef = this.modalService.openModal(modal);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((event: string) => {
        if (event === SeModalOutputEvents.MAIN_ACTION) {
          this.deleteTaxDeclarationInformation(maintainYear);
          this.setModelCode(this.componentForm.get('modelCode')?.value);
          this.saveData().subscribe({
            next: () => {
              // Después de guardar los datos, verificar si necesitamos buscar substitutiva
              this.verifyCodeInsurance(this.componentForm.get('model')?.value);
            },
          });
          modalRef.close();
        } else {
          this.resetFormsValues(modalRef);
        }
      });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.resetFormsValues(modalRef);
      });
  }

  private resetFormsValues(modalRef: NgbModalRef): void {
    this.componentForm.get('modelCode')?.setValue(this.currentModelCode);
    this.componentForm.get('exercici')?.setValue(this.currentYear);
    this.fileUploadPanelTitle = this.getModelDescription(this.currentModelCode);
    this.verifyCodeInsurance(this.componentForm.get('model')?.value);
    this.downloadFileTemplateUrl = this.translateService.instant(
      'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.FILE_UPLOAD_PANEL.DESCRIPTION_2',
      {
        url: this.taxYearDeclarationService.getModelUrl(
          this.componentForm.get('model')?.value,
          this.translateService.currentLang as 'ca' | 'es',
        ),
      },
    );
    modalRef.close();
  }

  private deleteTaxDeclarationInformation(maintainYear: boolean = false): void {
    if (!maintainYear) {
      this.componentForm.get('exercici')?.setValue('');
      this.showPanelFileUpload = false;
    }
    const fileId: string = this.componentForm
      .get('anex')
      ?.get('idDocument')?.value;
      console.log('Attempting to delete file with ID:', fileId);

    if (fileId) {
      this.deleteFileWithRetry(fileId);

      // Reset form data immediately to prevent UI inconsistencies
      this.componentForm.get('anex')?.reset();
      this.showValidateFileErrorsId = null;
    }

    this.cleanSubstitutiveData();
  }

  /**
   * Deletes a file with retry logic to handle network cancellations
   * @param fileId - The ID of the file to delete
   */
  private deleteFileWithRetry(fileId: string): void {
    console.log('Initiating file deletion with retry logic for ID:', fileId);

    // Try the observable approach first (for compatibility with the upload component)
    timer(100)
      .pipe(
        takeUntil(this.destroyed$),
        catchError((error) => {
          console.warn('Observable approach failed, trying direct HTTP call:', error);
          return this.deleteFileDirectly(fileId);
        })
      )
      .subscribe({
        next: () => {
          console.log('Emitting delete request for file ID:', fileId);
          this.deleteFileByDocId$.next(fileId);
        },
        error: (error) => {
          console.error('Unexpected error in file deletion:', error);
          // Fallback to direct HTTP call
          this.deleteFileDirectly(fileId).subscribe({
            next: () => console.log('File deleted successfully via direct HTTP call'),
            error: (httpError) => console.error('Direct HTTP deletion also failed:', httpError)
          });
        }
      });
  }

  /**
   * Deletes a file directly via HTTP service with retry logic
   * @param fileId - The ID of the file to delete
   */
  private deleteFileDirectly(fileId: string): Observable<any> {
    console.log('Attempting direct file deletion for ID:', fileId);

    return this.taxDeclarationEndpointService.deleteFileByDocId(fileId)
      .pipe(
        takeUntil(this.destroyed$),
        retry({
          count: 3, // Retry up to 3 times
          delay: (error, retryCount) => {
            console.warn(`Direct file deletion attempt ${retryCount} failed, retrying in ${retryCount * 1000}ms...`, error);
            return timer(retryCount * 1000); // Exponential backoff: 1s, 2s, 3s
          }
        }),
        catchError((error) => {
          console.error('Direct file deletion failed after all retry attempts:', error);
          // You could show a user notification here
          return of(null); // Return empty observable to prevent error propagation
        })
      );
  }

  private getTaxYearsByImpost(model: string): void {
    this.taxDeclarationEndpointService
      .getTaxYearsByImpost(model)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            this.optionsTaxYearList = response.content.map((year: string) => ({
              id: year,
              label: year,
            }));
          }
        },
        error: (error) => {
          console.error('Error fetching tax years:', error);
        },
      });
  }

  protected openProgressModal(idValidation: string): void {
    const progressValue$ = new BehaviorSubject<number>(1);

    const progressModal: SeProgressModal = {
      interval: 2,
      subtitle: '',
      message: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.DESCRIPTION',
      ),
      customButton: {
        label: this.translateService.instant('UI_COMPONENTS.BUTTONS.CANCEL'),
        size: 'small',
        btnTheme: 'trueOnlyText',
      },
      progressValue$: progressValue$,
    };

    const modalRef = this.modalService.openProgressModal(
      progressModal.interval!,
      progressModal.message!,
      progressModal.subtitle,
      progressModal.progressValue$,
      progressModal.customButton ?? undefined,
    );

    this.checkValidationProgress(modalRef, idValidation, progressValue$);
  }

  private checkValidationProgress = (
    modalRef: NgbModalRef,
    idValidation: string,
    progressValue$: BehaviorSubject<number>,
  ): void => {
    const intervalSubscription = modalRef.componentInstance.intervalOutput
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.taxDeclarationEndpointService
          .getValidationStatus(idValidation)
          .pipe(takeUntil(this.destroyed$))
          .subscribe({
            next: (response) => {
              const data: ValidationProgress | undefined = response?.content;
              if (
                data &&
                data?.percentatgeProgres >= 100 &&
                data?.estat === EstatFitxer.VALIDAT
              ) {
                progressValue$.next(100);
                this.closeProgressSubscription(
                  modalRef,
                  intervalSubscription,
                  progressValue$,
                );
                this.openSuccessModal();
              } else if (
                data &&
                (data?.estat === EstatFitxer.VALIDACIO_ERROR ||
                  data.estat === EstatFitxer.CANCELLAT)
              ) {
                progressValue$.next(100);
                this.closeProgressSubscription(
                  modalRef,
                  intervalSubscription,
                  progressValue$,
                );
                if (data.estat === EstatFitxer.VALIDACIO_ERROR) {
                  this.showValidateFileErrorsId = idValidation;
                }
              } else {
                progressValue$.next(data?.percentatgeProgres || 0);
              }
            },
            error: (error) => {
              console.error('Error getting validation status:', error);
              this.closeProgressSubscription(
                modalRef,
                intervalSubscription,
                progressValue$,
              );
            },
          });
      });

    modalRef.componentInstance.onCustomButton
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.cancelFileValidation(idValidation);
        this.closeProgressSubscription(
          modalRef,
          intervalSubscription,
          progressValue$,
        );
      });
  };

  private cancelFileValidation(idValidation: string): void {
    this.taxDeclarationEndpointService
      .patchCancelFileValidation(idValidation)
      .pipe(takeUntil(this.destroyed$))
      .subscribe();
  }

  private closeProgressSubscription(
    modalRef: NgbModalRef,
    intervalSubscription: Subscription,
    progressValue$: BehaviorSubject<number>,
  ): void {
    progressValue$.complete();
    intervalSubscription.unsubscribe();
    modalRef.close();
  }

  protected onFilesLoaded(event: Event): void {
    const documentsAdded: iDocumentPadoct[] =
      (event as CustomEvent).detail || [];
    if (documentsAdded.length > 0) {
      const anexData = documentsAdded[0];
      this.componentForm.get('anex')?.patchValue({
        idPadoct: anexData.idPadoct,
        nom: anexData.nom,
        descripcio: anexData.description,
        pes: anexData.size,
        idDocument: anexData.id,
        extension: anexData.format?.split('/')[1],
        tipusDocument: Constant.TAX_DOCUMENT_TYPE,
      });
      this.saveData().subscribe();
    } else {
      this.componentForm.get('anex')?.reset();
      this.showValidateFileErrorsId = null;
    }
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }

  private cleanSubstitutiveData(): void {
    this.substitutiveData = {
      showPanel: false,
      substitutive: null,
      samePresenter: false,
      hasDeclaracioPrevia: false,
    };
    this.componentForm.get('substitutiva')?.reset();
    this.componentForm
      .get('substitutiva')
      ?.get('numJustificant')
      ?.clearValidators();
    this.componentForm
      .get('substitutiva')
      ?.get('numJustificant')
      ?.updateValueAndValidity();
  }

  protected searchSubstituteiveDeclaration(): void {
    this.cleanSubstitutiveData();
    this.taxDeclarationEndpointService
      .getLastPresenterSubstitutiva(this.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            const { hasDeclaracioPrevia, declaracio } = response.content;
            const numJustificantForm = this.componentForm
              .get('substitutiva')
              ?.get('numJustificant');
            if (hasDeclaracioPrevia && declaracio) {
              this.substitutiveData = {
                substitutive: declaracio,
                samePresenter: true,
                showPanel: true,
                hasDeclaracioPrevia: true,
              };
              numJustificantForm?.setValidators([Validators.required]);
              numJustificantForm?.updateValueAndValidity();
              this.componentForm
                .get('substitutiva')
                ?.patchValue(this.substitutiveData.substitutive);
            } else if (hasDeclaracioPrevia) {
              // Substitutive declaration exists but different presenter
              this.substitutiveData = {
                substitutive: null,
                samePresenter: false,
                showPanel: true,
                hasDeclaracioPrevia: true,
              };
              numJustificantForm?.setValidators([Validators.required]);
              numJustificantForm?.updateValueAndValidity();
            } else if (
              this.componentForm.get('exercici')?.value <
              Constant.NEW_SUBSTITUTIVE_YEAR
            ) {
              // No hemos encontrado datos pero si el ejercicio es anterior a 2025 mostramos panel de búsqueda manual de substitutiva
              this.substitutiveData = {
                substitutive: null,
                samePresenter: false,
                showPanel: true,
                hasDeclaracioPrevia: false,
              };
            }

            this.cdr.detectChanges();
          }
        },
        error: (error) => {
          console.error('Error fetching substitutive declaration:', error);
        },
      });
  }

  private saveData(): Observable<SeHttpResponse> {
    const file: File = this.componentForm.get('anex')?.value;
    const body: PutTaxDeclarationData = {
      model: this.componentForm.get('model')?.value,
      exercici: this.componentForm.get('exercici')?.value,
      substitutiva: this.componentForm.get('substitutiva')?.value,
      codiImpost: this.componentForm.get('codiImpost')?.value,
      dadaAdicional: this.componentForm.get('dadaAdicional')?.value,
      fitxer: file,
    };
    return this.taxDeclarationEndpointService
      .putTaxDeclarationData(body, this.idTramit!)
      .pipe(takeUntil(this.destroyed$));
  }

  private openSuccessModal(): void {
    const modal: SeModal = {
      severity: 'success',
      title: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.SUCCESS.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.PROGRESS_MODAL.SUCCESS.DESCRIPTION',
      ),
      closable: true,
      closableLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CONTINUE',
      ),
      size: 'lg',
    };

    const modalRef = this.modalService.openModal(modal);
    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((event: string) => {
        if (event === SeModalOutputEvents.MAIN_ACTION) {
          modalRef.close();
          this.submit();
        }
      });
  }

  private submit(): void {
    this.headerService.model = this.componentForm.get('model')?.value;
    this.headerService.taxYear = this.componentForm.get('exercici')?.value;
    this.headerService.substitutiva = this.componentForm
      .get('substitutiva')
      ?.get('numJustificant')?.value;
    this.headerService.status = SelfAssessmentState.IN_PROGRESS;
    this.storeService.modelDescription = this.fileUploadPanelTitle;
    this.router.navigate([AppRoutes.SUMMARY]);
  }

  protected validateBeforeSubmit(): void {
    this.showValidateFileErrorsId = null;
    this.taxDeclarationEndpointService
      .postValidateTaxDeclaration(this.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            this.openProgressModal(response.content);
          }
        },
        error: (error) => {
          console.error('Error validating tax declaration:', error);
        },
      });
  }
}
