import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { PresentationResultGuard } from './presentation-result.guard';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

describe('PresentationResultGuard', () => {
  let guard: PresentationResultGuard;
  let storeService: jasmine.SpyObj<StoreService>;
  let router: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const storeServiceSpy = jasmine.createSpyObj('StoreService', [], {
      numJustificant: null,
      modelDescription: null,
      idTramit: null,
    });
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        PresentationResultGuard,
        { provide: StoreService, useValue: storeServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    });

    guard = TestBed.inject(PresentationResultGuard);
    storeService = TestBed.inject(StoreService) as jasmine.SpyObj<StoreService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow access when numJustificant exists', () => {
    // Arrange
    Object.defineProperty(storeService, 'numJustificant', {
      get: () => 'test-num-justificant',
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('should redirect to SUMMARY when numJustificant does not exist but modelDescription exists', () => {
    // Arrange
    Object.defineProperty(storeService, 'numJustificant', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'modelDescription', {
      get: () => 'Test Model Description',
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith([AppRoutes.SUMMARY]);
  });

  it('should redirect to YEAR_DECLARATION when numJustificant and modelDescription do not exist but idTramit exists', () => {
    // Arrange
    Object.defineProperty(storeService, 'numJustificant', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'modelDescription', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'idTramit', {
      get: () => 'test-id-tramit',
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith([AppRoutes.YEAR_DECLARATION]);
  });

  it('should redirect to PARTICIPANTS when none of the required values exist', () => {
    // Arrange
    Object.defineProperty(storeService, 'numJustificant', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'modelDescription', {
      get: () => null,
    });
    Object.defineProperty(storeService, 'idTramit', {
      get: () => null,
    });

    // Act
    const result = guard.canActivate();

    // Assert
    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith([AppRoutes.PARTICIPANTS]);
  });
});
