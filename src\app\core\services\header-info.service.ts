import { formatDate } from '@angular/common';
import { Inject, Injectable, LOCALE_ID, type On<PERSON><PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Subject, combineLatest, take, takeUntil } from 'rxjs';

import {
  SeTagThemeEnum,
  type Nullable,
  type SeHeaderInfoItem,
  type TagProps,
} from 'se-ui-components-mf-lib';
import { StoreService } from './store.service';
import {
  HeaderTagData,
  SelfAssessmentState,
  SelfAssessmentStatus,
  SelfAssessmentStatusTranslations,
} from '@core/models/self-assessment-status.model';

@Injectable({
  providedIn: 'root',
})
export class HeaderInfoService implements OnDestroy {
  private readonly INFO_ITEMS_INITIAL_VALUE = [
    null, // Nombre del sujeto pasivo
    null, // Modelo
    null, // status
    null, // ejercicio
    null, // sustitutiva
  ];
  private readonly infoItemsSubject = new BehaviorSubject<
    Nullable<SeHeaderInfoItem>[]
  >(this.INFO_ITEMS_INITIAL_VALUE);
  public readonly infoItems$ = this.infoItemsSubject.asObservable();

  private readonly tagsSubject = new BehaviorSubject<Nullable<TagProps[]>>([]);
  public readonly tags$ = this.tagsSubject.asObservable();

  private presentationDateSubject = new BehaviorSubject<Nullable<string>>(null);
  private presentationDate$ = this.presentationDateSubject.asObservable();

  private readonly TRANSLATE_PATH = 'SE_DECINF_MF.HEADER_INFO';
  private translations$ = this.translate.get(this.TRANSLATE_PATH);
  private _receiptId: Nullable<string>;
  private destroyed$ = new Subject<void>();

  constructor(
    private translate: TranslateService,
    @Inject(LOCALE_ID) private locale: string,
    private store: StoreService,
  ) {
    this.translations$.pipe(take(1)).subscribe((translations) => {
      this.handleLanguageChange(translations);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.unsubscribe();
  }

  private handleLanguageChange(translations: string[]): void {
    const currentLang = this.translate.currentLang;
    if (!this.store?.currentLang || this.store?.currentLang !== currentLang) {
      this.store.currentLang = currentLang;
      this.replaceTerms(this.store.header, translations);
    } else {
      this.getStoredData(translations);
    }
  }

  private replaceTerms(
    headers: Nullable<SeHeaderInfoItem>[],
    translations: string[],
  ): void {
    const translationKeys = Object.keys(translations);
    let keyIndex = 0;

    const updatedHeaders =
      headers?.map((headerItem) => {
        if (headerItem === null || keyIndex >= translationKeys.length) {
          return headerItem;
        }
        const translationKey = `${this.TRANSLATE_PATH}.${translationKeys[keyIndex]}`;
        keyIndex++;
        return {
          ...headerItem,
          term: this.translate.instant(translationKey),
        };
      }) || this.INFO_ITEMS_INITIAL_VALUE;
    this.store.header = updatedHeaders;
    this.getStoredData(translations);
  }

  private getStoredData(translations: string[]): void {
    if (this.store.header) {
      this.infoItemsSubject.next(this.store.header);
      const status = this.store?.status;
      const presentationDate = this.store?.presentationDate;
      this.presentationDateSubject.next(presentationDate);
      this.tagsSubject.next(
        this.createTags({ translations, presentationDate, status }),
      );
    }
  }

  public resetYearDeclaration(): void {
    this.resetInfoItems(1);
  }

  public resetCompleteSubheader(): void {
    this.resetInfoItems(0);
    this.tagsSubject.next([]);
  }

  private resetInfoItems(start: number): void {
    const newItems = this.infoItemsSubject.getValue();
    if (newItems) {
      newItems.fill(null, start, 6);
      this.infoItemsSubject.next([...newItems]);
      this.tagsSubject.next([]);
      this.presentationDateSubject.next(null);
    }
  }

  public set taxpayerName(name: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(0, 1, {
          term: translations['TAXPAYER'],
          details: name,
        });
      });
  }

  public set taxYear(year: Nullable<string | number>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(3, 1, {
          term: translations['EXERCICI'],
          details: `${String(year)}`,
        });
      });
  }

  public set model(model: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const modelInfoItem = model
          ? { term: translations['MODEL'], details: model }
          : null;
        this.spliceItems(2, 1, modelInfoItem);
      });
  }

  public set substitutiva(substitutiva: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const modelInfoItem = substitutiva
          ? { term: translations['SUBSTITUTE'], details: substitutiva }
          : null;
        this.spliceItems(4, 1, modelInfoItem);
      });
  }

  public set status(status: Nullable<SelfAssessmentStatus>) {
    combineLatest([this.translations$, this.presentationDate$])
      .pipe(takeUntil(this.destroyed$))
      .subscribe(([translations, presentationDate]) => {
        if (!status) {
          this.tagsSubject.next([]);
          return;
        }
        this.store.status = status;
        this.tagsSubject.next(
          this.createTags({ translations, presentationDate, status }),
        );
      });
  }

  private createTags({
    translations,
    presentationDate,
    status,
  }: HeaderTagData): TagProps[] {
    switch (status) {
      case SelfAssessmentState.IN_PROGRESS: {
        return [
          {
            label: (translations as SelfAssessmentStatusTranslations).STATE
              .IN_PROGRESS,
            tagTheme: SeTagThemeEnum.WARNING,
          },
        ];
      }

      case SelfAssessmentState.PRESENTAT: {
        let label = (translations as SelfAssessmentStatusTranslations).STATE
          .PRESENTAT;
        if (presentationDate) {
          const date = formatDate(presentationDate, 'dd/MM/yyyy', this.locale);
          label += `: ${date}`;
        }
        const tags = [{ label, tagTheme: SeTagThemeEnum.SUCCESS }];
        return tags;
      }

      default: {
        return [];
      }
    }
  }

  public set receiptId(id: Nullable<string>) {
    this._receiptId = id;

    if (!id) {
      this.spliceItems(6, 1, null);
      return;
    }

    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(6, 1, {
          term: translations['SUBSTITUTE'],
          details: id,
        });
      });
  }

  public get receiptId(): Nullable<string> {
    return this._receiptId;
  }

  private spliceItems(
    start: number,
    deleteCount: number,
    ...items: Nullable<SeHeaderInfoItem>[]
  ): void {
    const newItems = this.infoItemsSubject.getValue();
    if (newItems) {
      newItems.splice(start, deleteCount, ...items);
      this.infoItemsSubject.next([...newItems]);
      this.store.header = [...newItems];
    }
  }

  get presentationDate(): Nullable<string> {
    return this.presentationDateSubject.getValue();
  }
  set presentationDate(date: Nullable<string>) {
    this.presentationDateSubject.next(date);
    this.store.presentationDate = date;
  }
}
