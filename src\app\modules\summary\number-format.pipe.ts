// number-format.pipe.ts
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'numberFormat',
})
export class NumberFormatPipe implements PipeTransform {
  transform(
    value: string | number | boolean,
    decimalPlaces: number = 2,
  ): string {
    if (value === null || value === undefined) return '';
    if (typeof value === 'string') return value;
    if (typeof value !== 'number') return String(value);

    // Formatear el número con separadores
    return new Intl.NumberFormat('es-ES', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
      useGrouping: true,
    }).format(value);
  }
}
