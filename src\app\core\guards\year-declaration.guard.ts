import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

@Injectable({
  providedIn: 'root',
})
export class YearDeclarationGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private router: Router,
  ) {}

  canActivate(): boolean {
    const idTramit = this.storeService.idTramit;

    if (idTramit) {
      return true;
    }

    this.router.navigate([AppRoutes.PARTICIPANTS]);
    return false;
  }
}
